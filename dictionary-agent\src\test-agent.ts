import { mastra } from './mastra';

async function testDictionaryAgent() {
  try {
    console.log('Testing Dictionary Agent...');
    
    // Test with a simple word
    const testWord = 'hello';
    console.log(`\nLooking up definition for: "${testWord}"`);
    
    const result = await mastra.agents.dictionaryAgent.generate(
      `Please provide the definition for the word: ${testWord}`
    );
    
    console.log('\nAgent Response:');
    console.log(result.text);
    
  } catch (error) {
    console.error('Error testing dictionary agent:', error);
  }
}

// Run the test
testDictionaryAgent();
