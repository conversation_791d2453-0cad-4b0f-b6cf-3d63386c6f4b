import { mastra } from '../mastra';

export async function POST(request: Request) {
  try {
    const { word } = await request.json();
    
    if (!word) {
      return new Response(
        JSON.stringify({ error: 'Word parameter is required' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log(`Looking up definition for: "${word}"`);
    
    const result = await mastra.agents.dictionaryAgent.generate(
      `Please provide the definition for the word: ${word}`
    );
    
    return new Response(
      JSON.stringify({ 
        word,
        definition: result.text 
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
  } catch (error) {
    console.error('Error in dictionary API:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
