
> @mastra/memory@0.10.0-alpha.1 build /home/<USER>/work/mastra/mastra/packages/memory
> pnpm run check && tsup src/index.ts src/processors/index.ts --format esm,cjs --experimental-dts --clean --treeshake=smallest --splitting


> @mastra/memory@0.10.0-alpha.1 check /home/<USER>/work/mastra/mastra/packages/memory
> tsc --noEmit

[34mCLI[39m Building entry: src/index.ts, src/processors/index.ts
[34mCLI[39m Using tsconfig: tsconfig.json
[34mCLI[39m tsup v8.4.0
[34mTSC[39m Build start
[32mTSC[39m ⚡️ Build success in 9942ms
[34mDTS[39m Build start
[34mCLI[39m Target: es2022
Analysis will use the bundled TypeScript version 5.8.3
[36mWriting package typings: /home/<USER>/work/mastra/mastra/packages/memory/dist/_tsup-dts-rollup.d.ts[39m
Analysis will use the bundled TypeScript version 5.8.3
[36mWriting package typings: /home/<USER>/work/mastra/mastra/packages/memory/dist/_tsup-dts-rollup.d.cts[39m
[32mDTS[39m ⚡️ Build success in 11520ms
[34mCLI[39m Cleaning output folder
[34mESM[39m Build start
[34mCJS[39m Build start
[32mCJS[39m [1mdist/index.cjs            [22m[32m19.00 KB[39m
[32mCJS[39m [1mdist/processors/index.cjs [22m[32m5.59 KB[39m
[32mCJS[39m ⚡️ Build success in 941ms
[32mESM[39m [1mdist/index.js            [22m[32m18.81 KB[39m
[32mESM[39m [1mdist/processors/index.js [22m[32m5.38 KB[39m
[32mESM[39m ⚡️ Build success in 955ms
