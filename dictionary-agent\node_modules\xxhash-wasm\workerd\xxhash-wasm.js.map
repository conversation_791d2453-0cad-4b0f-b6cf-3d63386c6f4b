{"version": 3, "file": "xxhash-wasm.js", "sources": ["../src/index.workerd.js", "../src/xxhash.js"], "sourcesContent": ["import { xxhash } from \"./xxhash\";\n\n// In CloudFlare workerd we must use import to prevent code injection.\nimport module from \"./xxhash.wasm\";\n\nexport default async function () {\n  return xxhash(await WebAssembly.instantiate(module));\n}\n", "const u32_BYTES = 4;\nconst u64_BYTES = 8;\n\n// The xxh32 hash state struct:\nconst XXH32_STATE_SIZE_BYTES =\n  u32_BYTES + // total_len\n  u32_BYTES + // large_len\n  u32_BYTES * 4 + // Accumulator lanes\n  u32_BYTES * 4 + // Internal buffer\n  u32_BYTES + // memsize\n  u32_BYTES; // reserved\n\n// The xxh64 hash state struct:\nconst XXH64_STATE_SIZE_BYTES =\n  u64_BYTES + // total_len\n  u64_BYTES * 4 + // Accumulator lanes\n  u64_BYTES * 4 + // Internal buffer\n  u32_BYTES + // memsize\n  u32_BYTES + // reserved32\n  u64_BYTES; // reserved64\n\nexport function xxhash(instance) {\n  const {\n    exports: {\n      mem,\n      xxh32,\n      xxh64,\n      init32,\n      update32,\n      digest32,\n      init64,\n      update64,\n      digest64,\n    },\n  } = instance;\n\n  let memory = new Uint8Array(mem.buffer);\n  // Grow the wasm linear memory to accommodate length + offset bytes\n  function growMemory(length, offset) {\n    if (mem.buffer.byteLength < length + offset) {\n      const extraPages = Math.ceil(\n        // Wasm pages are spec'd to 64K\n        (length + offset - mem.buffer.byteLength) / (64 * 1024),\n      );\n      mem.grow(extraPages);\n      // After growing, the original memory's ArrayBuffer is detached, so we'll\n      // need to replace our view over it with a new one over the new backing\n      // ArrayBuffer.\n      memory = new Uint8Array(mem.buffer);\n    }\n  }\n\n  // The h32 and h64 streaming hash APIs are identical, so we can implement\n  // them both by way of a templated call to this generalized function.\n  function create(size, seed, init, update, digest, finalize) {\n    // Ensure that we've actually got enough space in the wasm memory to store\n    // the state blob for this hasher.\n    growMemory(size);\n\n    // We'll hold our hashing state in this closure.\n    const state = new Uint8Array(size);\n    memory.set(state);\n    init(0, seed);\n\n    // Each time we interact with wasm, it may have mutated our state so we'll\n    // need to read it back into our closed copy.\n    state.set(memory.subarray(0, size));\n\n    return {\n      update(input) {\n        memory.set(state);\n        let length;\n        if (typeof input === \"string\") {\n          growMemory(input.length * 3, size);\n          length = encoder.encodeInto(input, memory.subarray(size)).written;\n        } else {\n          // The only other valid input type is a Uint8Array\n          growMemory(input.byteLength, size);\n          memory.set(input, size);\n          length = input.byteLength;\n        }\n        update(0, size, length);\n        state.set(memory.subarray(0, size));\n        return this;\n      },\n      digest() {\n        memory.set(state);\n        return finalize(digest(0));\n      },\n    };\n  }\n\n  // Logical shift right makes it an u32, otherwise it's interpreted as an i32.\n  function forceUnsigned32(i) {\n    return i >>> 0;\n  }\n\n  // BigInts are arbitrary precision and signed, so to get the \"correct\" u64\n  // value from the return, we'll need to force that interpretation.\n  const u64Max = 2n ** 64n - 1n;\n  function forceUnsigned64(i) {\n    return i & u64Max;\n  }\n\n  const encoder = new TextEncoder();\n  const defaultSeed = 0;\n  const defaultBigSeed = 0n;\n\n  function h32(str, seed = defaultSeed) {\n    // https://developer.mozilla.org/en-US/docs/Web/API/TextEncoder/encodeInto#buffer_sizing\n    // By sizing the buffer to 3 * string-length we guarantee that the buffer\n    // will be appropriately sized for the utf-8 encoding of the string.\n    growMemory(str.length * 3, 0);\n    return forceUnsigned32(\n      xxh32(0, encoder.encodeInto(str, memory).written, seed),\n    );\n  }\n\n  function h64(str, seed = defaultBigSeed) {\n    growMemory(str.length * 3, 0);\n    return forceUnsigned64(\n      xxh64(0, encoder.encodeInto(str, memory).written, seed),\n    );\n  }\n\n  return {\n    h32,\n    h32ToString(str, seed = defaultSeed) {\n      return h32(str, seed).toString(16).padStart(8, \"0\");\n    },\n    h32Raw(inputBuffer, seed = defaultSeed) {\n      growMemory(inputBuffer.byteLength, 0);\n      memory.set(inputBuffer);\n      return forceUnsigned32(xxh32(0, inputBuffer.byteLength, seed));\n    },\n    create32(seed = defaultSeed) {\n      return create(\n        XXH32_STATE_SIZE_BYTES,\n        seed,\n        init32,\n        update32,\n        digest32,\n        forceUnsigned32,\n      );\n    },\n    h64,\n    h64ToString(str, seed = defaultBigSeed) {\n      return h64(str, seed).toString(16).padStart(16, \"0\");\n    },\n    h64Raw(inputBuffer, seed = defaultBigSeed) {\n      growMemory(inputBuffer.byteLength, 0);\n      memory.set(inputBuffer);\n      return forceUnsigned64(xxh64(0, inputBuffer.byteLength, seed));\n    },\n    create64(seed = defaultBigSeed) {\n      return create(\n        XXH64_STATE_SIZE_BYTES,\n        seed,\n        init64,\n        update64,\n        digest64,\n        forceUnsigned64,\n      );\n    },\n  };\n}\n"], "names": ["index_workerd", "xxhash", "instance", "exports", "mem", "xxh32", "xxh64", "init32", "update32", "digest32", "init64", "update64", "digest64", "memory", "Uint8Array", "buffer", "growMemory", "length", "offset", "byteLength", "extraPages", "Math", "ceil", "grow", "create", "size", "seed", "init", "update", "digest", "finalize", "state", "set", "subarray", "input", "encoder", "encodeInto", "written", "forceUnsigned32", "i", "u64Max", "forceUnsigned64", "TextEncoder", "defaultSeed", "defaultBigSeed", "h32", "str", "h64", "h32ToString", "toString", "padStart", "h32Raw", "inputBuffer", "create32", "u32_BYTES", "h64ToString", "h64Raw", "create64", "u64_BYTES", "WebAssembly", "instantiate", "module"], "mappings": "6BAKe,eAAAA,IACb,OAAOC,ACeF,SAAgBC,CAAQ,EAC7B,KAAM,CACJC,QAAS,CACPC,IAAAA,CAAG,CACHC,MAAAA,CAAK,CACLC,MAAAA,CAAK,CACLC,OAAAA,CAAM,CACNC,SAAAA,CAAQ,CACRC,SAAAA,CAAQ,CACRC,OAAAA,CAAM,CACNC,SAAAA,CAAQ,CACRC,SAAAA,CAAQ,CACT,CACF,CAAGV,EAEJ,IAAIW,EAAS,IAAIC,WAAWV,EAAIW,MAAM,EAEtC,SAASC,EAAWC,CAAM,CAAEC,CAAM,EAChC,GAAId,EAAIW,MAAM,CAACI,UAAU,CAAGF,EAASC,EAAQ,CAC3C,MAAME,EAAaC,KAAKC,IAAI,CAE1B,AAACL,CAAAA,EAASC,EAASd,EAAIW,MAAM,CAACI,UAAU,AAAA,EAAK,MAE/Cf,CAAAA,EAAImB,IAAI,CAACH,GAITP,EAAS,IAAIC,WAAWV,EAAIW,MAAM,CACpC,CACF,CAIA,SAASS,EAAOC,CAAI,CAAEC,CAAI,CAAEC,CAAI,CAAEC,CAAM,CAAEC,CAAM,CAAEC,CAAQ,EAGxDd,EAAWS,GAGX,MAAMM,EAAQ,IAAIjB,WAAWW,GAQ7B,OAPAZ,EAAOmB,GAAG,CAACD,GACXJ,EAAK,EAAGD,GAIRK,EAAMC,GAAG,CAACnB,EAAOoB,QAAQ,CAAC,EAAGR,IAEtB,CACLG,OAAOM,CAAK,EAENjB,IAAAA,EAYJ,OAbAJ,EAAOmB,GAAG,CAACD,GAEU,UAAjB,OAAOG,EACTlB,CAAAA,EAA0B,EAAfkB,EAAMjB,MAAM,CAAMQ,GAC7BR,EAASkB,EAAQC,UAAU,CAACF,EAAOrB,EAAOoB,QAAQ,CAACR,IAAOY,OAAO,AAAA,EAGjErB,CAAAA,EAAWkB,EAAMf,UAAU,CAAEM,GAC7BZ,EAAOmB,GAAG,CAACE,EAAOT,GAClBR,EAASiB,EAAMf,UAAU,AAAVA,EAEjBS,EAAO,EAAGH,EAAMR,GAChBc,EAAMC,GAAG,CAACnB,EAAOoB,QAAQ,CAAC,EAAGR,IACtB,IAAI,AACb,EACAI,OAAAA,IACEhB,CAAAA,EAAOmB,GAAG,CAACD,GACJD,EAASD,EAAO,GAAA,CAE3B,CACF,CAGA,SAASS,EAAgBC,CAAC,EACxB,OAAOA,IAAM,CACf,CAIA,MAAMC,EAAS,CAAE,AAAF,CAAE,EAAI,EAAG,AAAH,CAAG,CAAG,CAAE,AAAF,CAAE,CAC7B,SAASC,EAAgBF,CAAC,EACxB,OAAOA,EAAIC,CACb,CAEA,MAAML,EAAU,IAAIO,YACdC,EAAc,EACdC,EAAiB,CAAE,AAAF,CAAE,CAEzB,SAASC,EAAIC,CAAG,CAAEpB,EAAOiB,CAAW,EAKlC,OADA3B,EAAW8B,EAAAA,EAAI7B,MAAM,CAAM,GACpBqB,EACLjC,EAAM,EAAG8B,EAAQC,UAAU,CAACU,EAAKjC,GAAQwB,OAAO,CAAEX,GAEtD,CAEA,SAASqB,EAAID,CAAG,CAAEpB,EAAOkB,CAAc,EAErC,OADA5B,EAAW8B,EAAAA,EAAI7B,MAAM,CAAM,GACpBwB,EACLnC,EAAM,EAAG6B,EAAQC,UAAU,CAACU,EAAKjC,GAAQwB,OAAO,CAAEX,GAEtD,CAEA,MAAO,CACLmB,IAAAA,EACAG,YAAAA,CAAYF,EAAKpB,EAAOiB,CAAW,GAC1BE,EAAIC,EAAKpB,GAAMuB,QAAQ,CAAC,IAAIC,QAAQ,CAAC,EAAG,KAEjDC,OAAAA,CAAOC,EAAa1B,EAAOiB,CAAW,GACpC3B,CAAAA,EAAWoC,EAAYjC,UAAU,CAAE,GACnCN,EAAOmB,GAAG,CAACoB,GACJd,EAAgBjC,EAAM,EAAG+C,EAAYjC,UAAU,CAAEO,GAAAA,EAE1D2B,SAAAA,CAAS3B,EAAOiB,CAAW,GAClBnB,EAnIX8B,GAqIM5B,EACAnB,EACAC,EACAC,EACA6B,GAGJS,IAAAA,EACAQ,YAAAA,CAAYT,EAAKpB,EAAOkB,CAAc,GAC7BG,EAAID,EAAKpB,GAAMuB,QAAQ,CAAC,IAAIC,QAAQ,CAAC,GAAI,KAElDM,OAAAA,CAAOJ,EAAa1B,EAAOkB,CAAc,GACvC5B,CAAAA,EAAWoC,EAAYjC,UAAU,CAAE,GACnCN,EAAOmB,GAAG,CAACoB,GACJX,EAAgBnC,EAAM,EAAG8C,EAAYjC,UAAU,CAAEO,GAAAA,EAE1D+B,SAAAA,CAAS/B,EAAOkB,CAAc,GACrBpB,EA7IXkC,GA+IMhC,EACAhB,EACAC,EACAC,EACA6B,EAGN,CACF,ED/JgB,MAAMkB,YAAYC,WAAW,CAACC,GAC9C"}