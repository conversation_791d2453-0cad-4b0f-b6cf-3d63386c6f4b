#!/usr/bin/env node

import { mastra } from './mastra';

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: npm run cli <word>');
    console.log('Example: npm run cli hello');
    process.exit(1);
  }
  
  const word = args[0];
  
  try {
    console.log(`🔍 Looking up definition for: "${word}"`);
    console.log('⏳ Please wait...\n');
    
    const result = await mastra.agents.dictionaryAgent.generate(
      `Please provide the definition for the word: ${word}`
    );
    
    console.log('📖 Definition:');
    console.log('─'.repeat(50));
    console.log(result.text);
    console.log('─'.repeat(50));
    
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

main();
