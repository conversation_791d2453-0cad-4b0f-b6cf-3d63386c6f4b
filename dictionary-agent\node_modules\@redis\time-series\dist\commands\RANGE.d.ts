import { RedisCommandArguments } from '@redis/client/dist/lib/commands';
import { RangeOptions, Timestamp, SampleRawReply, SampleReply } from '.';
export declare const FIRST_KEY_INDEX = 1;
export declare const IS_READ_ONLY = true;
export declare function transformArguments(key: string, fromTimestamp: Timestamp, toTimestamp: Timestamp, options?: RangeOptions): RedisCommandArguments;
export declare function transformReply(reply: Array<SampleRawReply>): Array<SampleReply>;
