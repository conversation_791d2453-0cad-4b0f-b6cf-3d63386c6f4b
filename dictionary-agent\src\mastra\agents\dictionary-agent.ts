import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { getDictionaryTools } from '../tools';

export async function createDictionaryAgent() {
  // Get the MCP tools
  const dictionaryTools = await getDictionaryTools();

  // Create the agent with the MCP tools
  const agent = new Agent({
    name: 'Dictionary Agent',
    instructions: `You are a helpful dictionary agent that provides word definitions.
    
When a user provides a word, use the get_definitions tool to look up the word's definitions.
Return the definitions in a clear, readable format.

If the word is not found or there are no definitions available, let the user know politely.

Always be helpful and provide accurate information from the dictionary service.`,
    model: openai('gpt-4'),
    tools: dictionaryTools,
  });

  return agent;
}

export const dictionaryAgent = await createDictionaryAgent();
