{"name": "dictionary-agent", "version": "1.0.0", "main": "index.js", "scripts": {"test": "tsx src/test-agent.ts", "dev": "<PERSON>ra dev", "build": "mastra build", "cli": "tsx src/cli.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@mastra/core": "^0.10.0", "@mastra/libsql": "^0.10.0", "@mastra/mcp": "^0.10.0", "@mastra/memory": "^0.10.0", "zod": "^3.25.30"}, "devDependencies": {"@types/node": "^22.15.21", "mastra": "^0.10.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}}