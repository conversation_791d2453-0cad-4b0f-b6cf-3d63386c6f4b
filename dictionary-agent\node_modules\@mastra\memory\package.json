{"name": "@mastra/memory", "version": "0.10.0", "description": "", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./processors": {"import": {"types": "./dist/processors/index.d.ts", "default": "./dist/processors/index.js"}, "require": {"types": "./dist/processors/index.d.cts", "default": "./dist/processors/index.cjs"}}, "./package.json": "./package.json"}, "keywords": [], "author": "", "license": "Elastic-2.0", "dependencies": {"@upstash/redis": "^1.34.5", "ai": "^4.2.2", "js-tiktoken": "^1.0.19", "pg": "^8.13.3", "pg-pool": "^3.7.1", "postgres": "^3.4.5", "redis": "^4.7.0", "xxhash-wasm": "^1.1.0", "zod": "^3.24.3"}, "devDependencies": {"@ai-sdk/openai": "^1.3.3", "@microsoft/api-extractor": "^7.52.5", "@types/node": "^20.17.27", "@types/pg": "^8.11.11", "eslint": "^9.23.0", "tsup": "^8.4.0", "typescript": "^5.8.2", "typescript-eslint": "^8.26.1", "vitest": "^3.1.2", "@internal/lint": "0.0.6", "@mastra/core": "0.10.0"}, "peerDependencies": {"@mastra/core": "^0.10.0"}, "scripts": {"check": "tsc --noEmit", "build": "pnpm run check && tsup src/index.ts src/processors/index.ts --format esm,cjs --experimental-dts --clean --treeshake=smallest --splitting", "build:watch": "pnpm build --watch", "test:integration": "cd integration-tests && pnpm run test", "test:unit": "pnpm vitest run ./src/*", "test": "pnpm test:integration && pnpm test:unit", "lint": "eslint ."}}